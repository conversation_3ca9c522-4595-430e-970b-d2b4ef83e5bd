import bpy
import math
import random

# Limpiar escena
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete()

# Configurar renderizado
bpy.context.scene.render.engine = 'CYCLES'
bpy.context.scene.cycles.device = 'GPU'
bpy.context.scene.cycles.samples = 128

# Crear suelo
bpy.ops.mesh.primitive_plane_add(size=50, enter_editmode=False, align='WORLD', location=(0, 0, 0))
suelo = bpy.context.active_object
suelo.name = "Suelo_Centro_Comercial"

# Material para el suelo (asfalto del estacionamiento)
def crear_material_asfalto():
    mat_asfalto = bpy.data.materials.new(name="Material_Asfalto")
    mat_asfalto.use_nodes = True
    nodes = mat_asfalto.node_tree.nodes
    links = mat_asfalto.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    noise_texture = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    bump = nodes.new(type='ShaderNodeBump')
    
    # Configurar nodos
    noise_texture.inputs['Scale'].default_value = 50.0
    noise_texture.inputs['Detail'].default_value = 8.0
    
    # Configurar ColorRamp para variaciones de gris
    color_ramp.color_ramp.elements[0].position = 0.4
    color_ramp.color_ramp.elements[0].color = (0.1, 0.1, 0.1, 1.0)  # Gris oscuro
    color_ramp.color_ramp.elements[1].position = 0.6
    color_ramp.color_ramp.elements[1].color = (0.2, 0.2, 0.2, 1.0)  # Gris medio
    
    # Configurar Principled BSDF
    principled.inputs['Roughness'].default_value = 0.9
    principled.inputs['Specular'].default_value = 0.1
    
    # Configurar Bump
    bump.inputs['Strength'].default_value = 0.2
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
    links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(noise_texture.outputs['Fac'], bump.inputs['Height'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(bump.outputs['Normal'], principled.inputs['Normal'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    return mat_asfalto

# Aplicar material al suelo
suelo.data.materials.append(crear_material_asfalto())

# Función para crear el edificio principal del centro comercial
def crear_edificio_principal():
    # Base del edificio
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 0, 3))
    edificio = bpy.context.active_object
    edificio.name = "Edificio_Principal"
    edificio.scale = (15, 10, 3)
    
    # Techo
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 0, 6.5))
    techo = bpy.context.active_object
    techo.name = "Techo_Principal"
    techo.scale = (15.5, 10.5, 0.5)
    
    # Material para el edificio (concreto moderno)
    mat_edificio = bpy.data.materials.new(name="Material_Edificio")
    mat_edificio.use_nodes = True
    nodes = mat_edificio.node_tree.nodes
    links = mat_edificio.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    noise_texture = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    
    # Configurar nodos
    noise_texture.inputs['Scale'].default_value = 20.0
    noise_texture.inputs['Detail'].default_value = 4.0
    
    # Configurar ColorRamp para variaciones de beige
    color_ramp.color_ramp.elements[0].position = 0.4
    color_ramp.color_ramp.elements[0].color = (0.7, 0.65, 0.6, 1.0)  # Beige oscuro
    color_ramp.color_ramp.elements[1].position = 0.6
    color_ramp.color_ramp.elements[1].color = (0.8, 0.75, 0.7, 1.0)  # Beige claro
    
    # Configurar Principled BSDF
    principled.inputs['Roughness'].default_value = 0.7
    principled.inputs['Specular'].default_value = 0.2
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
    links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Material para el techo (gris)
    mat_techo = bpy.data.materials.new(name="Material_Techo")
    mat_techo.use_nodes = True
    nodes = mat_techo.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.3, 0.3, 1.0)  # Gris
    bsdf.inputs[7].default_value = 0.8  # Rugosidad
    
    # Aplicar materiales
    edificio.data.materials.append(mat_edificio)
    techo.data.materials.append(mat_techo)
    
    return [edificio, techo]

# Función para crear la entrada principal (más pegada)
def crear_entrada():
    # Estructura de la entrada (más pegada al edificio)
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 10.25, 2.5))
    entrada = bpy.context.active_object
    entrada.name = "Entrada_Principal"
    entrada.scale = (8, 0.25, 2.5)

    # Techo de la entrada (más pegado)
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 10.75, 5.5))
    techo_entrada = bpy.context.active_object
    techo_entrada.name = "Techo_Entrada"
    techo_entrada.scale = (9, 0.75, 0.5)

    # Columnas (más pegadas)
    columnas = []
    for x in [-6, 6]:
        bpy.ops.mesh.primitive_cylinder_add(radius=0.4, depth=5,
                                          enter_editmode=False,
                                          location=(x, 10.25, 2.5))
        columna = bpy.context.active_object
        columna.name = f"Columna_{x}"
        columnas.append(columna)
    
    # Material para la entrada (vidrio)
    mat_entrada = bpy.data.materials.new(name="Material_Entrada")
    mat_entrada.use_nodes = True
    nodes = mat_entrada.node_tree.nodes
    links = mat_entrada.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    glass = nodes.new(type='ShaderNodeBsdfGlass')
    
    # Configurar vidrio
    glass.inputs['Color'].default_value = (0.8, 0.9, 1.0, 1.0)  # Azulado claro
    glass.inputs['Roughness'].default_value = 0.05
    glass.inputs['IOR'].default_value = 1.45
    
    # Conectar nodos
    links.new(glass.outputs['BSDF'], output.inputs['Surface'])
    
    # Material para columnas (concreto)
    mat_columna = bpy.data.materials.new(name="Material_Columna")
    mat_columna.use_nodes = True
    nodes = mat_columna.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.8, 0.8, 0.8, 1.0)  # Blanco
    bsdf.inputs[7].default_value = 0.5  # Rugosidad
    
    # Aplicar materiales
    entrada.data.materials.append(mat_entrada)
    techo_entrada.data.materials.append(mat_columna)
    for columna in columnas:
        columna.data.materials.append(mat_columna)
    
    return [entrada, techo_entrada] + columnas

# Función para crear ventanas (fusionadas con las paredes usando operaciones booleanas)
def crear_ventanas(edificio_principal):
    ventanas = []
    edificio_obj = edificio_principal[0]  # El edificio principal

    # Ventanas frontales (crear huecos en la pared frontal)
    for x in [-12, -8, -4, 0, 4, 8, 12]:
        # Crear el hueco de la ventana
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(x, 10, 3))
        hueco_ventana = bpy.context.active_object
        hueco_ventana.name = f"Hueco_Ventana_Frontal_{x}"
        hueco_ventana.scale = (1.5, 1, 1.6)

        # Aplicar operación booleana para crear el hueco
        bool_modifier = edificio_obj.modifiers.new(name=f"Bool_Ventana_F_{x}", type='BOOLEAN')
        bool_modifier.operation = 'DIFFERENCE'
        bool_modifier.object = hueco_ventana

        # Crear el vidrio de la ventana (ligeramente dentro del hueco)
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(x, 9.9, 3))
        vidrio = bpy.context.active_object
        vidrio.name = f"Vidrio_Frontal_{x}"
        vidrio.scale = (1.4, 0.05, 1.5)
        ventanas.append(vidrio)

        # Ocultar el objeto del hueco (ya no se necesita visualmente)
        hueco_ventana.hide_viewport = True

    # Ventanas laterales
    for y in [-7, -3.5, 0, 3.5, 7]:
        # Lado izquierdo
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(-15, y, 3))
        hueco_izq = bpy.context.active_object
        hueco_izq.name = f"Hueco_Ventana_Izq_{y}"
        hueco_izq.scale = (1, 1.2, 1.6)

        bool_modifier_izq = edificio_obj.modifiers.new(name=f"Bool_Ventana_I_{y}", type='BOOLEAN')
        bool_modifier_izq.operation = 'DIFFERENCE'
        bool_modifier_izq.object = hueco_izq

        # Vidrio izquierdo
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(-14.9, y, 3))
        vidrio_izq = bpy.context.active_object
        vidrio_izq.name = f"Vidrio_Izquierdo_{y}"
        vidrio_izq.scale = (0.05, 1.1, 1.5)
        ventanas.append(vidrio_izq)

        hueco_izq.hide_viewport = True

        # Lado derecho
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(15, y, 3))
        hueco_der = bpy.context.active_object
        hueco_der.name = f"Hueco_Ventana_Der_{y}"
        hueco_der.scale = (1, 1.2, 1.6)

        bool_modifier_der = edificio_obj.modifiers.new(name=f"Bool_Ventana_D_{y}", type='BOOLEAN')
        bool_modifier_der.operation = 'DIFFERENCE'
        bool_modifier_der.object = hueco_der

        # Vidrio derecho
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(14.9, y, 3))
        vidrio_der = bpy.context.active_object
        vidrio_der.name = f"Vidrio_Derecho_{y}"
        vidrio_der.scale = (0.05, 1.1, 1.5)
        ventanas.append(vidrio_der)

        hueco_der.hide_viewport = True
    
    # Material para ventanas (vidrio)
    mat_vidrio = bpy.data.materials.new(name="Material_Vidrio")
    mat_vidrio.use_nodes = True
    nodes = mat_vidrio.node_tree.nodes
    links = mat_vidrio.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    glass = nodes.new(type='ShaderNodeBsdfGlass')
    
    # Configurar vidrio
    glass.inputs['Color'].default_value = (0.8, 0.9, 1.0, 1.0)  # Azulado claro
    glass.inputs['Roughness'].default_value = 0.05
    glass.inputs['IOR'].default_value = 1.45
    
    # Conectar nodos
    links.new(glass.outputs['BSDF'], output.inputs['Surface'])
    
    # Aplicar material a todas las ventanas
    for ventana in ventanas:
        ventana.data.materials.append(mat_vidrio)
    
    return ventanas

# Función para crear estacionamiento
def crear_estacionamiento():
    # Líneas de estacionamiento
    lineas = []
    
    # Crear líneas horizontales
    for y in range(-20, -5, 3):
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, y, 0.01))
        linea = bpy.context.active_object
        linea.name = f"Linea_Estacionamiento_{y}"
        linea.scale = (12, 0.1, 0.01)
        lineas.append(linea)
    
    # Crear líneas verticales
    for x in range(-12, 13, 3):
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(x, -12.5, 0.01))
        linea = bpy.context.active_object
        linea.name = f"Linea_Estacionamiento_V_{x}"
        linea.scale = (0.1, 7.5, 0.01)
        lineas.append(linea)
    
    # Material para líneas (blanco)
    mat_linea = bpy.data.materials.new(name="Material_Linea")
    mat_linea.use_nodes = True
    nodes = mat_linea.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (1.0, 1.0, 1.0, 1.0)  # Blanco
    bsdf.inputs[7].default_value = 0.5  # Rugosidad
    
    # Aplicar material a todas las líneas
    for linea in lineas:
        linea.data.materials.append(mat_linea)
    
    return lineas

# Función para crear letreros
def crear_letreros():
    # Letrero principal
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 11, 8))
    letrero = bpy.context.active_object
    letrero.name = "Letrero_Principal"
    letrero.scale = (10, 0.2, 1)
    
    # Soporte del letrero
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 10.5, 7))
    soporte = bpy.context.active_object
    soporte.name = "Soporte_Letrero"
    soporte.scale = (0.5, 0.5, 1)
    
    # Material para el letrero (colorido)
    mat_letrero = bpy.data.materials.new(name="Material_Letrero")
    mat_letrero.use_nodes = True
    nodes = mat_letrero.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.8, 0.1, 0.1, 1.0)  # Rojo
    bsdf.inputs[7].default_value = 0.3  # Rugosidad
    bsdf.inputs[19].default_value = 0.5  # Emisión
    
    # Material para el soporte
    mat_soporte = bpy.data.materials.new(name="Material_Soporte")
    mat_soporte.use_nodes = True
    nodes = mat_soporte.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.2, 0.2, 0.2, 1.0)  # Gris oscuro
    bsdf.inputs[7].default_value = 0.5  # Rugosidad
    
    # Aplicar materiales
    letrero.data.materials.append(mat_letrero)
    soporte.data.materials.append(mat_soporte)
    
    return [letrero, soporte]

# Función para crear árboles decorativos
def crear_arbol(location, altura=None):
    if altura is None:
        altura = random.uniform(2, 3)
    
    # Tronco
    bpy.ops.mesh.primitive_cylinder_add(radius=0.2, depth=altura, 
                                      enter_editmode=False, 
                                      location=(location[0], location[1], altura/2))
    tronco = bpy.context.active_object
    tronco.name = f"Tronco_{location[0]}_{location[1]}"
    
    # Material para tronco
    mat_tronco = bpy.data.materials.new(name=f"Material_Tronco_{location[0]}_{location[1]}")
    mat_tronco.use_nodes = True
    nodes = mat_tronco.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.2, 0.1, 1.0)  # Marrón
    bsdf.inputs[7].default_value = 0.8  # Rugosidad
    tronco.data.materials.append(mat_tronco)
    
    # Copa del árbol
    bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(0.8, 1.2), 
                                        enter_editmode=False, 
                                        location=(location[0], location[1], altura + 0.8))
    copa = bpy.context.active_object
    copa.name = f"Copa_{location[0]}_{location[1]}"
    
    # Material para copa
    mat_copa = bpy.data.materials.new(name=f"Material_Copa_{location[0]}_{location[1]}")
    mat_copa.use_nodes = True
    nodes = mat_copa.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    verde = random.uniform(0.2, 0.4)
    bsdf.inputs[0].default_value = (0.1, verde, 0.1, 1.0)  # Verde variado
    copa.data.materials.append(mat_copa)
    
    return tronco, copa

# Función para crear bancos y elementos decorativos
def crear_mobiliario_urbano():
    mobiliario = []
    
    # Crear bancos
    for x in [-10, 0, 10]:
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(x, 15, 0.5))
        banco = bpy.context.active_object
        banco.name = f"Banco_{x}"
        banco.scale = (1.5, 0.5, 0.5)
        mobiliario.append(banco)
    
    # Crear papeleras
    for x in [-5, 5]:
        bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=1, 
                                          enter_editmode=False, 
                                          location=(x, 15, 0.5))
        papelera = bpy.context.active_object
        papelera.name = f"Papelera_{x}"
        mobiliario.append(papelera)
    
    # Material para bancos (madera)
    mat_banco = bpy.data.materials.new(name="Material_Banco")
    mat_banco.use_nodes = True
    nodes = mat_banco.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.4, 0.25, 0.1, 1.0)  # Marrón madera
    bsdf.inputs[7].default_value = 0.6  # Rugosidad
    
    # Material para papeleras (metal)
    mat_papelera = bpy.data.materials.new(name="Material_Papelera")
    mat_papelera.use_nodes = True
    nodes = mat_papelera.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.3, 0.3, 1.0)  # Gris
    bsdf.inputs[4].default_value = 0.8  # Metálico
    bsdf.inputs[7].default_value = 0.2  # Rugosidad
    
    # Aplicar materiales
    for elemento in mobiliario:
        if "Banco" in elemento.name:
            elemento.data.materials.append(mat_banco)
        else:
            elemento.data.materials.append(mat_papelera)
    
    return mobiliario

# Función para aplicar modificadores booleanos
def aplicar_modificadores_booleanos():
    # Aplicar todos los modificadores booleanos al edificio principal
    for obj in bpy.data.objects:
        if "Edificio_Principal" in obj.name:
            # Deseleccionar todo primero
            bpy.ops.object.select_all(action='DESELECT')

            # Seleccionar el objeto
            bpy.context.view_layer.objects.active = obj
            obj.select_set(True)

            # Aplicar todos los modificadores booleanos
            modifiers_to_apply = []
            for modifier in obj.modifiers:
                if modifier.type == 'BOOLEAN':
                    modifiers_to_apply.append(modifier.name)

            # Aplicar modificadores uno por uno
            for mod_name in modifiers_to_apply:
                try:
                    bpy.ops.object.modifier_apply(modifier=mod_name)
                except:
                    print(f"No se pudo aplicar el modificador {mod_name}")

            obj.select_set(False)
            break

# Crear elementos de la escena
edificio = crear_edificio_principal()
entrada = crear_entrada()
ventanas = crear_ventanas(edificio)

# Aplicar los modificadores booleanos para que los huecos se vean
aplicar_modificadores_booleanos()

estacionamiento = crear_estacionamiento()
letreros = crear_letreros()
mobiliario = crear_mobiliario_urbano()

# Crear árboles decorativos
arboles = []
# Árboles en la entrada
for x in range(-20, 21, 5):
    if abs(x) > 8:  # Evitar la entrada principal
        arbol = crear_arbol((x, 15, 0))
        arboles.append(arbol)

# Árboles en el estacionamiento
for _ in range(5):
    x = random.choice([-10.5, -7.5, -4.5, -1.5, 1.5, 4.5, 7.5, 10.5])
    y = random.choice([-19, -16, -13, -10, -7])
    arbol = crear_arbol((x, y, 0))
    arboles.append(arbol)

# Añadir iluminación
# Sol principal
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 30))
sol = bpy.context.active_object
sol.name = "Sol"
sol.data.energy = 3
sol.rotation_euler = (math.radians(60), 0, math.radians(30))

# Luz de relleno para sombras suaves
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 30))
luz_relleno = bpy.context.active_object
luz_relleno.name = "Luz_Relleno"
luz_relleno.data.energy = 1
luz_relleno.rotation_euler = (math.radians(60), 0, math.radians(210))

# Luces de la entrada (para efecto nocturno)
for x in [-5, 5]:
    bpy.ops.object.light_add(type='POINT', radius=1, location=(x, 10, 5))
    luz_entrada = bpy.context.active_object
    luz_entrada.name = f"Luz_Entrada_{x}"
    luz_entrada.data.energy = 50
    luz_entrada.data.color = (1.0, 0.9, 0.7)  # Luz cálida

# Añadir cámara
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW', 
                        location=(30, 30, 20), 
                        rotation=(math.radians(60), 0, math.radians(135)))
camara = bpy.context.active_object
camara.name = "Camara_Centro_Comercial"
bpy.context.scene.camera = camara

# Configurar fondo de cielo
world = bpy.context.scene.world
world.use_nodes = True
bg = world.node_tree.nodes['Background']
bg.inputs[0].default_value = (0.5, 0.7, 1.0, 1.0)  # Azul cielo
bg.inputs[1].default_value = 1.0  # Intensidad

print("¡Maqueta de centro comercial creada con éxito!")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
