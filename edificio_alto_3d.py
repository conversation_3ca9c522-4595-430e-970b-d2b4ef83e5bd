import bpy
import math
import random

# Limpiar escena
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete()

# Configurar renderizado
bpy.context.scene.render.engine = 'CYCLES'
bpy.context.scene.cycles.device = 'GPU'
bpy.context.scene.cycles.samples = 128

# Crear suelo
bpy.ops.mesh.primitive_plane_add(size=200, enter_editmode=False, align='WORLD', location=(0, 0, 0))
suelo = bpy.context.active_object
suelo.name = "Suelo_Ciudad"

# Material para el suelo
mat_suelo = bpy.data.materials.new(name="Material_Suelo")
mat_suelo.use_nodes = True
nodes = mat_suelo.node_tree.nodes
bsdf = nodes.get("Principled BSDF")
bsdf.inputs[0].default_value = (0.3, 0.3, 0.3, 1.0)  # Gris
bsdf.inputs[7].default_value = 0.7  # Rugosidad
suelo.data.materials.append(mat_suelo)

# Función para crear un edificio alto (rascacielos)
def crear_rascacielos(location, altura=100, ancho=20, profundidad=20):
    # Base del edificio
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False,
                                  location=(location[0], location[1], altura/2))
    edificio = bpy.context.active_object
    edificio.name = "Rascacielos"
    edificio.scale = (ancho/2, profundidad/2, altura/2)

    # Material para el edificio (vidrio y metal moderno)
    mat_edificio = bpy.data.materials.new(name="Material_Rascacielos")
    mat_edificio.use_nodes = True
    nodes = mat_edificio.node_tree.nodes
    links = mat_edificio.node_tree.links

    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)

    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    checker = nodes.new(type='ShaderNodeTexChecker')
    glass_shader = nodes.new(type='ShaderNodeBsdfGlass')
    mix_shader = nodes.new(type='ShaderNodeMixShader')
    fresnel = nodes.new(type='ShaderNodeFresnel')

    # Configurar nodos
    # Patrón de ventanas
    checker.inputs['Scale'].default_value = 50.0
    checker.inputs['Color1'].default_value = (0.05, 0.2, 0.3, 1.0)  # Azul oscuro
    checker.inputs['Color2'].default_value = (0.1, 0.3, 0.5, 1.0)   # Azul medio

    # Configurar vidrio
    glass_shader.inputs['Color'].default_value = (0.2, 0.5, 0.7, 1.0)  # Azul claro
    glass_shader.inputs['Roughness'].default_value = 0.05
    glass_shader.inputs['IOR'].default_value = 1.45  # Vidrio arquitectónico

    # Configurar Principled BSDF (para partes metálicas)
    principled.inputs['Base Color'].default_value = (0.8, 0.8, 0.8, 1.0)  # Plateado
    principled.inputs['Metallic'].default_value = 1.0
    principled.inputs['Roughness'].default_value = 0.1
    principled.inputs['Specular'].default_value = 0.9

    # Configurar Fresnel para efecto de reflejo variable según ángulo
    fresnel.inputs['IOR'].default_value = 1.8

    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], checker.inputs['Vector'])
    links.new(checker.outputs['Color'], glass_shader.inputs['Color'])
    links.new(fresnel.outputs['Fac'], mix_shader.inputs['Fac'])
    links.new(glass_shader.outputs['BSDF'], mix_shader.inputs[1])
    links.new(principled.outputs['BSDF'], mix_shader.inputs[2])
    links.new(mix_shader.outputs['Shader'], output.inputs['Surface'])

    edificio.data.materials.append(mat_edificio)

    # Crear detalles en el edificio (líneas de ventanas más pegadas)
    for i in range(1, int(altura/3)):
        z = i * 3

        # Línea horizontal alrededor del edificio (más pegada)
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False,
                                      location=(location[0], location[1], z))
        linea = bpy.context.active_object
        linea.name = f"Linea_Edificio_{z}"
        linea.scale = (ancho/2 + 0.05, profundidad/2 + 0.05, 0.05)

        # Material para las líneas (metal)
        mat_linea = bpy.data.materials.new(name=f"Material_Linea_{z}")
        mat_linea.use_nodes = True
        nodes = mat_linea.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (0.8, 0.8, 0.8, 1.0)  # Gris claro
        bsdf.inputs[4].default_value = 1.0  # Metálico
        bsdf.inputs[7].default_value = 0.1  # Rugosidad
        linea.data.materials.append(mat_linea)

    # Crear antena en la parte superior
    bpy.ops.mesh.primitive_cylinder_add(radius=1, depth=20,
                                      enter_editmode=False,
                                      location=(location[0], location[1], altura + 10))
    antena = bpy.context.active_object
    antena.name = "Antena_Rascacielos"

    # Material para la antena
    mat_antena = bpy.data.materials.new(name="Material_Antena")
    mat_antena.use_nodes = True
    nodes = mat_antena.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.2, 0.2, 0.2, 1.0)  # Gris oscuro
    bsdf.inputs[4].default_value = 1.0  # Metálico
    antena.data.materials.append(mat_antena)

    return edificio

# Crear el rascacielos principal
rascacielos = crear_rascacielos((0, 0, 0))

# Función para crear edificios más pequeños
def crear_edificio(location, altura, ancho, profundidad, tipo="oficina"):
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False,
                                  location=(location[0], location[1], altura/2))
    edificio = bpy.context.active_object
    edificio.name = f"Edificio_{location[0]}_{location[1]}"
    edificio.scale = (ancho/2, profundidad/2, altura/2)

    # Material según el tipo de edificio
    mat_edificio = bpy.data.materials.new(name=f"Material_Edificio_{location[0]}_{location[1]}")
    mat_edificio.use_nodes = True
    nodes = mat_edificio.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")

    if tipo == "oficina":
        # Edificio de oficinas (vidrio)
        bsdf.inputs[0].default_value = (0.2, 0.2, 0.3, 1.0)  # Azul grisáceo
        bsdf.inputs[4].default_value = 0.5  # Metálico
        bsdf.inputs[7].default_value = 0.1  # Rugosidad
        bsdf.inputs[14].default_value = 0.8  # Especular
    elif tipo == "apartamento":
        # Edificio de apartamentos (ladrillo)
        bsdf.inputs[0].default_value = (0.6, 0.3, 0.2, 1.0)  # Rojo ladrillo
        bsdf.inputs[7].default_value = 0.8  # Rugosidad
    elif tipo == "comercial":
        # Edificio comercial (colores variados)
        bsdf.inputs[0].default_value = (0.3, 0.5, 0.4, 1.0)  # Verde azulado
        bsdf.inputs[7].default_value = 0.3  # Rugosidad

    edificio.data.materials.append(mat_edificio)

    # Añadir ventanas (fusionadas con las paredes usando operaciones booleanas)
    if random.random() > 0.2:  # Más edificios tienen ventanas detalladas
        filas = int(altura / 3)
        columnas = int(ancho / 3)

        for i in range(filas):
            for j in range(columnas):
                # Solo en las caras visibles (frente y lado)
                # Frente - crear hueco en la pared
                hueco_x = location[0] + (j - columnas/2) * 3 + 1.5
                hueco_y = location[1] + profundidad/2
                hueco_z = i * 3 + 1.5

                # Crear el hueco de la ventana
                bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False,
                                              location=(hueco_x, hueco_y, hueco_z))
                hueco_ventana = bpy.context.active_object
                hueco_ventana.name = f"Hueco_Ventana_F_{location[0]}_{i}_{j}"
                hueco_ventana.scale = (0.8, 1, 0.8)

                # Aplicar operación booleana para crear el hueco
                bool_modifier = edificio.modifiers.new(name=f"Bool_V_F_{i}_{j}", type='BOOLEAN')
                bool_modifier.operation = 'DIFFERENCE'
                bool_modifier.object = hueco_ventana

                # Crear el vidrio de la ventana (dentro del hueco)
                bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False,
                                              location=(hueco_x, hueco_y - 0.1, hueco_z))
                vidrio = bpy.context.active_object
                vidrio.name = f"Vidrio_F_{location[0]}_{i}_{j}"
                vidrio.scale = (0.75, 0.1, 0.75)

                # Material para ventanas
                mat_ventana = bpy.data.materials.new(name=f"Material_Ventana_{location[0]}_{i}_{j}")
                mat_ventana.use_nodes = True
                nodes = mat_ventana.node_tree.nodes
                links = mat_ventana.node_tree.links

                # Limpiar nodos existentes
                for node in nodes:
                    nodes.remove(node)

                # Crear nodos para vidrio
                output = nodes.new(type='ShaderNodeOutputMaterial')
                glass = nodes.new(type='ShaderNodeBsdfGlass')

                # Ventanas iluminadas aleatoriamente
                if random.random() > 0.5:
                    glass.inputs['Color'].default_value = (1.0, 0.9, 0.5, 1.0)  # Amarillo (luz)
                else:
                    glass.inputs['Color'].default_value = (0.8, 0.9, 1.0, 1.0)  # Azul claro

                glass.inputs['Roughness'].default_value = 0.05
                glass.inputs['IOR'].default_value = 1.45

                # Conectar nodos
                links.new(glass.outputs['BSDF'], output.inputs['Surface'])

                vidrio.data.materials.append(mat_ventana)

                # Ocultar el objeto del hueco
                hueco_ventana.hide_viewport = True

    return edificio

# Crear edificios alrededor del rascacielos
edificios = []
tipos = ["oficina", "apartamento", "comercial"]

# Generar edificios en una cuadrícula
for x in range(-3, 4):
    for y in range(-3, 4):
        # Evitar la posición del rascacielos central
        if x == 0 and y == 0:
            continue

        # Posición con cierta variación
        pos_x = x * 30 + random.uniform(-5, 5)
        pos_y = y * 30 + random.uniform(-5, 5)

        # Altura y dimensiones aleatorias
        altura = random.uniform(15, 50)
        ancho = random.uniform(10, 25)
        profundidad = random.uniform(10, 25)

        # Tipo aleatorio
        tipo = random.choice(tipos)

        # Crear edificio
        edificio = crear_edificio((pos_x, pos_y, 0), altura, ancho, profundidad, tipo)
        edificios.append(edificio)

# Función para aplicar modificadores booleanos
def aplicar_modificadores_booleanos():
    # Aplicar todos los modificadores booleanos a todos los edificios
    for obj in bpy.data.objects:
        if obj.type == 'MESH' and len(obj.modifiers) > 0:
            # Deseleccionar todo primero
            bpy.ops.object.select_all(action='DESELECT')

            # Seleccionar el objeto
            bpy.context.view_layer.objects.active = obj
            obj.select_set(True)

            # Aplicar todos los modificadores booleanos
            modifiers_to_apply = []
            for modifier in obj.modifiers:
                if modifier.type == 'BOOLEAN':
                    modifiers_to_apply.append(modifier.name)

            # Aplicar modificadores uno por uno
            for mod_name in modifiers_to_apply:
                try:
                    bpy.ops.object.modifier_apply(modifier=mod_name)
                except:
                    print(f"No se pudo aplicar el modificador {mod_name}")

            obj.select_set(False)

# Aplicar los modificadores booleanos para que los huecos se vean
aplicar_modificadores_booleanos()

# Función para crear carreteras
def crear_carretera(inicio, fin, ancho=10):
    # Calcular dirección y longitud
    direccion = (fin[0] - inicio[0], fin[1] - inicio[1])
    longitud = math.sqrt(direccion[0]**2 + direccion[1]**2)

    # Crear plano para la carretera
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, location=(
        inicio[0] + direccion[0]/2,
        inicio[1] + direccion[1]/2,
        0.01))  # Ligeramente por encima del suelo
    carretera = bpy.context.active_object
    carretera.name = f"Carretera_{inicio}_{fin}"

    # Escalar y rotar la carretera
    carretera.scale.x = longitud / 2
    carretera.scale.y = ancho / 2

    # Rotar para alinear con la dirección
    angulo = math.atan2(direccion[1], direccion[0])
    carretera.rotation_euler.z = angulo

    # Material para carretera
    mat_carretera = bpy.data.materials.new(name=f"Material_Carretera_{inicio}_{fin}")
    mat_carretera.use_nodes = True
    nodes = mat_carretera.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.1, 0.1, 0.1, 1.0)  # Negro
    bsdf.inputs[7].default_value = 0.9  # Rugosidad
    carretera.data.materials.append(mat_carretera)

    # Añadir líneas en la carretera
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, location=(
        inicio[0] + direccion[0]/2,
        inicio[1] + direccion[1]/2,
        0.02))
    linea = bpy.context.active_object
    linea.name = f"Linea_{inicio}_{fin}"

    # Escalar y rotar la línea
    linea.scale.x = longitud / 2
    linea.scale.y = 0.2
    linea.rotation_euler.z = angulo

    # Material para línea
    mat_linea = bpy.data.materials.new(name=f"Material_Linea_{inicio}_{fin}")
    mat_linea.use_nodes = True
    nodes = mat_linea.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (1.0, 1.0, 1.0, 1.0)  # Blanco
    linea.data.materials.append(mat_linea)

    return carretera, linea

# Crear carreteras en forma de cuadrícula
carreteras = []
for i in range(-3, 4):
    # Carreteras horizontales
    inicio_h = (i * 30, -100, 0)
    fin_h = (i * 30, 100, 0)
    carretera_h, linea_h = crear_carretera(inicio_h, fin_h)
    carreteras.append((carretera_h, linea_h))

    # Carreteras verticales
    inicio_v = (-100, i * 30, 0)
    fin_v = (100, i * 30, 0)
    carretera_v, linea_v = crear_carretera(inicio_v, fin_v)
    carreteras.append((carretera_v, linea_v))

# Función para crear árboles y vegetación
def crear_arbol(location, altura=None):
    if altura is None:
        altura = random.uniform(2, 5)

    # Tronco
    bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=altura,
                                      enter_editmode=False,
                                      location=(location[0], location[1], altura/2))
    tronco = bpy.context.active_object
    tronco.name = f"Tronco_{location[0]}_{location[1]}"

    # Material para tronco
    mat_tronco = bpy.data.materials.new(name=f"Material_Tronco_{location[0]}_{location[1]}")
    mat_tronco.use_nodes = True
    nodes = mat_tronco.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.2, 0.1, 1.0)  # Marrón
    tronco.data.materials.append(mat_tronco)

    # Copa del árbol
    bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(1, 2),
                                        enter_editmode=False,
                                        location=(location[0], location[1], altura + 1))
    copa = bpy.context.active_object
    copa.name = f"Copa_{location[0]}_{location[1]}"

    # Material para copa
    mat_copa = bpy.data.materials.new(name=f"Material_Copa_{location[0]}_{location[1]}")
    mat_copa.use_nodes = True
    nodes = mat_copa.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    verde = random.uniform(0.2, 0.4)
    bsdf.inputs[0].default_value = (0.1, verde, 0.1, 1.0)  # Verde variado
    copa.data.materials.append(mat_copa)

    return tronco, copa

# Añadir árboles a lo largo de algunas carreteras
arboles = []
for i in range(-3, 4, 2):  # Solo algunas calles tienen árboles
    for j in range(-90, 91, 10):
        # Árboles a lo largo de carreteras horizontales
        if random.random() > 0.3:
            pos_x = j
            pos_y = i * 30 + 7  # Desplazado del centro de la carretera
            arbol = crear_arbol((pos_x, pos_y, 0))
            arboles.append(arbol)

        # Árboles a lo largo de carreteras verticales
        if random.random() > 0.3:
            pos_x = i * 30 + 7  # Desplazado del centro de la carretera
            pos_y = j
            arbol = crear_arbol((pos_x, pos_y, 0))
            arboles.append(arbol)

# Añadir iluminación
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 200))
sol = bpy.context.active_object
sol.name = "Sol"
sol.data.energy = 3
sol.rotation_euler = (math.radians(60), 0, math.radians(30))

# Añadir iluminación ambiental nocturna
bpy.ops.object.light_add(type='AREA', radius=1, location=(0, 0, 50))
luz_ambiental = bpy.context.active_object
luz_ambiental.name = "Luz_Ambiental"
luz_ambiental.data.energy = 100
luz_ambiental.data.size = 200
luz_ambiental.data.color = (0.2, 0.3, 0.5)  # Azul nocturno

# Añadir cámara
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW', location=(100, -100, 80), rotation=(math.radians(60), 0, math.radians(45)))
camara = bpy.context.active_object
camara.name = "Camara_Ciudad"
bpy.context.scene.camera = camara

# Configurar fondo de cielo nocturno con estrellas
world = bpy.context.scene.world
world.use_nodes = True
nodes = world.node_tree.nodes
links = world.node_tree.links

# Limpiar nodos existentes
for node in nodes:
    nodes.remove(node)

# Crear nodos
background = nodes.new(type='ShaderNodeBackground')
output = nodes.new(type='ShaderNodeOutputWorld')
tex_coord = nodes.new(type='ShaderNodeTexCoord')
mapping = nodes.new(type='ShaderNodeMapping')
noise_texture = nodes.new(type='ShaderNodeTexNoise')
color_ramp = nodes.new(type='ShaderNodeValToRGB')
mix_rgb = nodes.new(type='ShaderNodeMixRGB')

# Configurar nodos
# Color base del cielo nocturno
background.inputs['Color'].default_value = (0.01, 0.01, 0.05, 1.0)  # Azul oscuro (noche)
background.inputs['Strength'].default_value = 1.0

# Configurar ruido para estrellas
noise_texture.inputs['Scale'].default_value = 500.0  # Muchas estrellas pequeñas
noise_texture.inputs['Detail'].default_value = 10.0
noise_texture.inputs['Roughness'].default_value = 0.7
noise_texture.inputs['Distortion'].default_value = 0.0

# Configurar ColorRamp para estrellas
color_ramp.color_ramp.elements[0].position = 0.95  # Solo los puntos más brillantes
color_ramp.color_ramp.elements[0].color = (0.0, 0.0, 0.0, 1.0)  # Negro (sin estrellas)
color_ramp.color_ramp.elements[1].position = 1.0
color_ramp.color_ramp.elements[1].color = (1.0, 1.0, 1.0, 1.0)  # Blanco (estrellas)

# Añadir un punto intermedio para estrellas más tenues
color_ramp.color_ramp.elements.new(0.98)
color_ramp.color_ramp.elements[1].color = (0.3, 0.3, 0.8, 1.0)  # Azul claro (estrellas tenues)

# Configurar mezcla
mix_rgb.blend_type = 'ADD'
mix_rgb.inputs['Fac'].default_value = 1.0
mix_rgb.inputs[1].default_value = (0.01, 0.01, 0.05, 1.0)  # Color base del cielo
# El segundo input vendrá del color_ramp

# Conectar nodos
links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
links.new(color_ramp.outputs['Color'], mix_rgb.inputs[2])
links.new(mix_rgb.outputs['Color'], background.inputs['Color'])
links.new(background.outputs['Background'], output.inputs['Surface'])

print("¡Maqueta del edificio alto con infraestructura creada con éxito!")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
