import bpy
import math
import random

# Limpiar escena
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete()

# Configurar renderizado
bpy.context.scene.render.engine = 'CYCLES'
bpy.context.scene.cycles.device = 'GPU'
bpy.context.scene.cycles.samples = 128

# Crear terreno
bpy.ops.mesh.primitive_plane_add(size=30, enter_editmode=False, align='WORLD', location=(0, 0, 0))
terreno = bpy.context.active_object
terreno.name = "Terreno"

# Material para el terreno (césped)
def crear_material_cesped():
    mat_cesped = bpy.data.materials.new(name="Material_Cesped")
    mat_cesped.use_nodes = True
    nodes = mat_cesped.node_tree.nodes
    links = mat_cesped.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    noise_texture = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    bump = nodes.new(type='ShaderNodeBump')
    
    # Configurar nodos
    noise_texture.inputs['Scale'].default_value = 50.0
    noise_texture.inputs['Detail'].default_value = 10.0
    
    # Configurar ColorRamp para variaciones de verde
    color_ramp.color_ramp.elements[0].position = 0.3
    color_ramp.color_ramp.elements[0].color = (0.05, 0.3, 0.05, 1.0)  # Verde oscuro
    color_ramp.color_ramp.elements[1].position = 0.7
    color_ramp.color_ramp.elements[1].color = (0.2, 0.5, 0.1, 1.0)    # Verde claro
    
    # Añadir un punto intermedio al ColorRamp
    color_ramp.color_ramp.elements.new(0.5)
    color_ramp.color_ramp.elements[1].color = (0.1, 0.4, 0.1, 1.0)    # Verde medio
    
    # Configurar Principled BSDF
    principled.inputs['Roughness'].default_value = 0.9
    principled.inputs['Specular'].default_value = 0.1
    
    # Configurar Bump
    bump.inputs['Strength'].default_value = 0.2
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
    links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(noise_texture.outputs['Fac'], bump.inputs['Height'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(bump.outputs['Normal'], principled.inputs['Normal'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    return mat_cesped

# Aplicar material al terreno
terreno.data.materials.append(crear_material_cesped())

# Función para crear la estructura principal de la casa
def crear_casa_principal():
    # Base de la casa (primer piso)
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 0, 1.5))
    base = bpy.context.active_object
    base.name = "Base_Casa"
    base.scale = (7, 5, 1.5)
    
    # Segundo piso
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 0, 3.5))
    segundo_piso = bpy.context.active_object
    segundo_piso.name = "Segundo_Piso"
    segundo_piso.scale = (7, 5, 1)
    
    # Techo
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 0, 5))
    techo = bpy.context.active_object
    techo.name = "Techo"
    techo.scale = (8, 6, 0.5)
    
    # Material para las paredes (blanco moderno)
    mat_paredes = bpy.data.materials.new(name="Material_Paredes")
    mat_paredes.use_nodes = True
    nodes = mat_paredes.node_tree.nodes
    links = mat_paredes.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    noise_texture = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    
    # Configurar nodos
    noise_texture.inputs['Scale'].default_value = 30.0
    noise_texture.inputs['Detail'].default_value = 6.0
    noise_texture.inputs['Roughness'].default_value = 0.7
    
    # Configurar ColorRamp para variaciones sutiles de blanco
    color_ramp.color_ramp.elements[0].position = 0.4
    color_ramp.color_ramp.elements[0].color = (0.9, 0.9, 0.9, 1.0)  # Casi blanco
    color_ramp.color_ramp.elements[1].position = 0.6
    color_ramp.color_ramp.elements[1].color = (1.0, 1.0, 1.0, 1.0)  # Blanco puro
    
    # Configurar Principled BSDF
    principled.inputs['Roughness'].default_value = 0.3
    principled.inputs['Specular'].default_value = 0.5
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
    links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Material para el techo (gris oscuro)
    mat_techo = bpy.data.materials.new(name="Material_Techo")
    mat_techo.use_nodes = True
    nodes = mat_techo.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.2, 0.2, 0.2, 1.0)  # Gris oscuro
    bsdf.inputs[7].default_value = 0.7  # Rugosidad
    
    # Aplicar materiales
    base.data.materials.append(mat_paredes)
    segundo_piso.data.materials.append(mat_paredes)
    techo.data.materials.append(mat_techo)
    
    return [base, segundo_piso, techo]

# Función para crear ventanas
def crear_ventanas(casa):
    ventanas = []
    
    # Ventanas del primer piso
    # Ventana frontal
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 5.01, 1.5))
    ventana_frontal = bpy.context.active_object
    ventana_frontal.name = "Ventana_Frontal_1"
    ventana_frontal.scale = (3, 0.05, 1)
    ventanas.append(ventana_frontal)
    
    # Ventanas laterales
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(7.01, 0, 1.5))
    ventana_lateral1 = bpy.context.active_object
    ventana_lateral1.name = "Ventana_Lateral_1"
    ventana_lateral1.scale = (0.05, 2, 1)
    ventanas.append(ventana_lateral1)
    
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(-7.01, 0, 1.5))
    ventana_lateral2 = bpy.context.active_object
    ventana_lateral2.name = "Ventana_Lateral_2"
    ventana_lateral2.scale = (0.05, 2, 1)
    ventanas.append(ventana_lateral2)
    
    # Ventanas del segundo piso
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 5.01, 3.5))
    ventana_frontal2 = bpy.context.active_object
    ventana_frontal2.name = "Ventana_Frontal_2"
    ventana_frontal2.scale = (4, 0.05, 0.7)
    ventanas.append(ventana_frontal2)
    
    # Material para ventanas (vidrio)
    mat_vidrio = bpy.data.materials.new(name="Material_Vidrio")
    mat_vidrio.use_nodes = True
    nodes = mat_vidrio.node_tree.nodes
    links = mat_vidrio.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    glass = nodes.new(type='ShaderNodeBsdfGlass')
    
    # Configurar vidrio
    glass.inputs['Color'].default_value = (0.8, 0.9, 1.0, 1.0)  # Azulado claro
    glass.inputs['Roughness'].default_value = 0.05
    glass.inputs['IOR'].default_value = 1.45
    
    # Conectar nodos
    links.new(glass.outputs['BSDF'], output.inputs['Surface'])
    
    # Aplicar material a todas las ventanas
    for ventana in ventanas:
        ventana.data.materials.append(mat_vidrio)
    
    return ventanas

# Función para crear puerta
def crear_puerta():
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(3, 5.01, 1))
    puerta = bpy.context.active_object
    puerta.name = "Puerta"
    puerta.scale = (1, 0.05, 1)
    
    # Material para la puerta (madera oscura)
    mat_puerta = bpy.data.materials.new(name="Material_Puerta")
    mat_puerta.use_nodes = True
    nodes = mat_puerta.node_tree.nodes
    links = mat_puerta.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    noise_texture = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    
    # Configurar nodos
    noise_texture.inputs['Scale'].default_value = 20.0
    noise_texture.inputs['Detail'].default_value = 10.0
    
    # Configurar ColorRamp para simular vetas de madera
    color_ramp.color_ramp.elements[0].position = 0.4
    color_ramp.color_ramp.elements[0].color = (0.2, 0.1, 0.05, 1.0)  # Marrón oscuro
    color_ramp.color_ramp.elements[1].position = 0.6
    color_ramp.color_ramp.elements[1].color = (0.3, 0.15, 0.05, 1.0)  # Marrón medio
    
    # Configurar Principled BSDF
    principled.inputs['Roughness'].default_value = 0.3
    principled.inputs['Specular'].default_value = 0.3
    principled.inputs['Clearcoat'].default_value = 0.2
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
    links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    puerta.data.materials.append(mat_puerta)
    
    return puerta

# Función para crear camino de entrada
def crear_camino():
    # Crear curva para el camino
    curva = bpy.data.curves.new('CaminoData', 'CURVE')
    curva.dimensions = '3D'
    
    spline = curva.splines.new('BEZIER')
    spline.bezier_points.add(2)  # Necesitamos 3 puntos para la curva
    
    # Punto inicial (entrada de la casa)
    spline.bezier_points[0].co = (3, 5, 0.01)
    spline.bezier_points[0].handle_left = (3, 5, 0.01)
    spline.bezier_points[0].handle_right = (3, 8, 0.01)
    
    # Punto medio
    spline.bezier_points[1].co = (0, 10, 0.01)
    spline.bezier_points[1].handle_left = (2, 10, 0.01)
    spline.bezier_points[1].handle_right = (-2, 10, 0.01)
    
    # Punto final (entrada del terreno)
    spline.bezier_points[2].co = (-3, 15, 0.01)
    spline.bezier_points[2].handle_left = (-3, 12, 0.01)
    spline.bezier_points[2].handle_right = (-3, 15, 0.01)
    
    # Crear objeto a partir de la curva
    obj_camino = bpy.data.objects.new('Camino', curva)
    bpy.context.collection.objects.link(obj_camino)
    
    # Dar volumen al camino
    curva.bevel_depth = 0.5
    curva.bevel_resolution = 4
    
    # Material para el camino (piedra)
    mat_camino = bpy.data.materials.new(name="Material_Camino")
    mat_camino.use_nodes = True
    nodes = mat_camino.node_tree.nodes
    links = mat_camino.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    noise_texture = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    bump = nodes.new(type='ShaderNodeBump')
    
    # Configurar nodos
    noise_texture.inputs['Scale'].default_value = 50.0
    noise_texture.inputs['Detail'].default_value = 15.0
    
    # Configurar ColorRamp para simular piedra
    color_ramp.color_ramp.elements[0].position = 0.4
    color_ramp.color_ramp.elements[0].color = (0.3, 0.3, 0.3, 1.0)  # Gris oscuro
    color_ramp.color_ramp.elements[1].position = 0.6
    color_ramp.color_ramp.elements[1].color = (0.5, 0.5, 0.5, 1.0)  # Gris claro
    
    # Configurar Principled BSDF
    principled.inputs['Roughness'].default_value = 0.7
    principled.inputs['Specular'].default_value = 0.2
    
    # Configurar Bump
    bump.inputs['Strength'].default_value = 0.5
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
    links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(noise_texture.outputs['Fac'], bump.inputs['Height'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(bump.outputs['Normal'], principled.inputs['Normal'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    obj_camino.data.materials.append(mat_camino)
    
    return obj_camino

# Función para crear árboles
def crear_arbol(location, altura=None):
    if altura is None:
        altura = random.uniform(3, 5)
    
    # Tronco
    bpy.ops.mesh.primitive_cylinder_add(radius=0.2, depth=altura, 
                                      enter_editmode=False, 
                                      location=(location[0], location[1], altura/2))
    tronco = bpy.context.active_object
    tronco.name = f"Tronco_{location[0]}_{location[1]}"
    
    # Material para tronco
    mat_tronco = bpy.data.materials.new(name=f"Material_Tronco_{location[0]}_{location[1]}")
    mat_tronco.use_nodes = True
    nodes = mat_tronco.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.2, 0.1, 1.0)  # Marrón
    bsdf.inputs[7].default_value = 0.8  # Rugosidad
    tronco.data.materials.append(mat_tronco)
    
    # Copa del árbol
    bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(1, 1.5), 
                                        enter_editmode=False, 
                                        location=(location[0], location[1], altura + 1))
    copa = bpy.context.active_object
    copa.name = f"Copa_{location[0]}_{location[1]}"
    
    # Material para copa
    mat_copa = bpy.data.materials.new(name=f"Material_Copa_{location[0]}_{location[1]}")
    mat_copa.use_nodes = True
    nodes = mat_copa.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    verde = random.uniform(0.2, 0.4)
    bsdf.inputs[0].default_value = (0.1, verde, 0.1, 1.0)  # Verde variado
    copa.data.materials.append(mat_copa)
    
    return tronco, copa

# Función para crear piscina
def crear_piscina():
    # Base de la piscina
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(-5, -5, 0))
    piscina = bpy.context.active_object
    piscina.name = "Piscina"
    piscina.scale = (3, 2, 0.5)
    
    # Agua de la piscina
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, location=(-5, -5, 0.5))
    agua = bpy.context.active_object
    agua.name = "Agua_Piscina"
    agua.scale = (2.9, 1.9, 1)
    
    # Material para la base de la piscina (azulejos)
    mat_piscina = bpy.data.materials.new(name="Material_Piscina")
    mat_piscina.use_nodes = True
    nodes = mat_piscina.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.8, 0.8, 0.8, 1.0)  # Blanco
    bsdf.inputs[7].default_value = 0.3  # Rugosidad
    piscina.data.materials.append(mat_piscina)
    
    # Material para el agua
    mat_agua = bpy.data.materials.new(name="Material_Agua_Piscina")
    mat_agua.use_nodes = True
    nodes = mat_agua.node_tree.nodes
    links = mat_agua.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Configurar agua de piscina
    principled.inputs['Base Color'].default_value = (0.0, 0.5, 0.8, 1.0)  # Azul piscina
    principled.inputs['Roughness'].default_value = 0.05
    principled.inputs['Specular'].default_value = 1.0
    principled.inputs['IOR'].default_value = 1.33
    principled.inputs['Transmission'].default_value = 0.9
    
    # Conectar nodos
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    agua.data.materials.append(mat_agua)
    
    return piscina, agua

# Crear elementos de la escena
casa = crear_casa_principal()
ventanas = crear_ventanas(casa)
puerta = crear_puerta()
camino = crear_camino()
piscina, agua_piscina = crear_piscina()

# Crear árboles decorativos
arboles = []
for _ in range(8):
    x = random.uniform(-12, 12)
    y = random.uniform(-12, 12)
    
    # Evitar colocar árboles donde hay estructuras
    if ((abs(x) < 8 and abs(y) < 6) or  # Casa
        (x > -8 and x < -2 and y > -8 and y < -2) or  # Piscina
        (x > 0 and x < 6 and y > 3 and y < 15)):  # Camino
        continue
    
    arbol = crear_arbol((x, y, 0))
    arboles.append(arbol)

# Añadir iluminación
# Sol principal
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 20))
sol = bpy.context.active_object
sol.name = "Sol"
sol.data.energy = 3
sol.rotation_euler = (math.radians(60), 0, math.radians(30))

# Luz de relleno para sombras suaves
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 20))
luz_relleno = bpy.context.active_object
luz_relleno.name = "Luz_Relleno"
luz_relleno.data.energy = 1
luz_relleno.rotation_euler = (math.radians(60), 0, math.radians(210))

# Añadir cámara
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW', 
                        location=(20, -20, 15), 
                        rotation=(math.radians(60), 0, math.radians(45)))
camara = bpy.context.active_object
camara.name = "Camara_Casa"
bpy.context.scene.camera = camara

# Configurar fondo de cielo
world = bpy.context.scene.world
world.use_nodes = True
bg = world.node_tree.nodes['Background']
bg.inputs[0].default_value = (0.5, 0.7, 1.0, 1.0)  # Azul cielo
bg.inputs[1].default_value = 1.0  # Intensidad

print("¡Maqueta de casa moderna creada con éxito!")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
