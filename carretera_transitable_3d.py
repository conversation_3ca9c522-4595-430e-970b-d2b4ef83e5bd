import bpy
import math
import random

# Limpiar escena
if bpy.context.mode != 'OBJECT':
    bpy.ops.object.mode_set(mode='OBJECT')

# <PERSON>leccionar todos los objetos
for obj in bpy.context.scene.objects:
    obj.select_set(True)

# Eliminar todos los objetos
bpy.ops.object.delete(use_global=False)

# Función para crear carretera principal con curvas
def crear_carretera_principal():
    # Crear curva bezier para la carretera
    bpy.ops.curve.primitive_bezier_curve_add(enter_editmode=False, location=(0, 0, 0))
    curva_carretera = bpy.context.active_object
    curva_carretera.name = "Curva_Carretera"
    
    # Modificar la curva para crear una carretera serpenteante
    bpy.context.view_layer.objects.active = curva_carretera
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Añadir puntos para crear curvas
    puntos_carretera = [
        (0, 0, 0), (20, 5, 0), (40, -10, 0), (60, 0, 0), 
        (80, 15, 0), (100, 10, 0), (120, -5, 0), (140, 0, 0)
    ]
    
    for i, punto in enumerate(puntos_carretera[1:]):
        bpy.ops.curve.extrude_move(TRANSFORM_OT_translate={"value": punto})
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Configurar propiedades de la curva
    curva_carretera.data.bevel_depth = 4.0  # Ancho de la carretera
    curva_carretera.data.bevel_resolution = 3
    curva_carretera.data.resolution_u = 20
    
    # Convertir a mesh
    bpy.ops.object.convert(target='MESH')
    carretera = bpy.context.active_object
    carretera.name = "Carretera_Principal"
    
    # Material para asfalto
    mat_asfalto = bpy.data.materials.new(name="Material_Asfalto")
    mat_asfalto.use_nodes = True
    nodes = mat_asfalto.node_tree.nodes
    links = mat_asfalto.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para asfalto realista
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise = nodes.new(type='ShaderNodeTexNoise')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    
    # Configurar propiedades del asfalto
    principled.inputs['Base Color'].default_value = (0.1, 0.1, 0.1, 1.0)  # Negro asfalto
    principled.inputs['Roughness'].default_value = 0.8
    principled.inputs['Specular'].default_value = 0.2
    
    # Configurar ruido para textura
    noise.inputs['Scale'].default_value = 50.0
    noise.inputs['Detail'].default_value = 10.0
    
    # ColorRamp para variación de color
    color_ramp.color_ramp.elements[0].color = (0.05, 0.05, 0.05, 1.0)
    color_ramp.color_ramp.elements[1].color = (0.15, 0.15, 0.15, 1.0)
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Aplicar material
    carretera.data.materials.append(mat_asfalto)
    
    return carretera

# Función para crear líneas de carretera
def crear_lineas_carretera():
    lineas = []
    
    # Línea central continua
    bpy.ops.curve.primitive_bezier_curve_add(enter_editmode=False, location=(0, 0, 0.01))
    linea_central = bpy.context.active_object
    linea_central.name = "Linea_Central"
    
    # Seguir la misma ruta que la carretera pero más estrecha
    bpy.context.view_layer.objects.active = linea_central
    bpy.ops.object.mode_set(mode='EDIT')
    
    puntos_linea = [
        (0, 0, 0.01), (20, 5, 0.01), (40, -10, 0.01), (60, 0, 0.01), 
        (80, 15, 0.01), (100, 10, 0.01), (120, -5, 0.01), (140, 0, 0.01)
    ]
    
    for punto in puntos_linea[1:]:
        bpy.ops.curve.extrude_move(TRANSFORM_OT_translate={"value": punto})
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Configurar línea central
    linea_central.data.bevel_depth = 0.1
    linea_central.data.bevel_resolution = 2
    linea_central.data.resolution_u = 20
    
    # Convertir a mesh
    bpy.ops.object.convert(target='MESH')
    linea_central.name = "Linea_Central_Mesh"
    
    # Material para líneas blancas
    mat_linea = bpy.data.materials.new(name="Material_Linea")
    mat_linea.use_nodes = True
    nodes = mat_linea.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (1.0, 1.0, 1.0, 1.0)  # Blanco
    bsdf.inputs[7].default_value = 0.3  # Rugosidad
    bsdf.inputs[19].default_value = 0.5  # Emisión para visibilidad
    
    linea_central.data.materials.append(mat_linea)
    lineas.append(linea_central)
    
    # Crear líneas laterales discontinuas
    for lado in [-1, 1]:
        for i in range(0, 140, 8):  # Líneas discontinuas cada 8 unidades
            bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, 
                                          location=(i, lado * 3.5, 0.01))
            linea_lateral = bpy.context.active_object
            linea_lateral.name = f"Linea_Lateral_{lado}_{i}"
            linea_lateral.scale = (3, 0.1, 0.01)
            linea_lateral.data.materials.append(mat_linea)
            lineas.append(linea_lateral)
    
    return lineas

# Función para crear señales de tráfico
def crear_señal_trafico(location, tipo="stop"):
    # Poste de la señal
    bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=3, 
                                      enter_editmode=False, 
                                      location=(location[0], location[1], 1.5))
    poste = bpy.context.active_object
    poste.name = f"Poste_Señal_{tipo}_{location[0]}"
    
    # Material para poste (metal)
    mat_poste = bpy.data.materials.new(name=f"Material_Poste_{tipo}")
    mat_poste.use_nodes = True
    nodes = mat_poste.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.3, 0.3, 1.0)  # Gris
    bsdf.inputs[4].default_value = 0.8  # Metálico
    bsdf.inputs[7].default_value = 0.2  # Rugosidad
    poste.data.materials.append(mat_poste)
    
    # Señal según el tipo
    if tipo == "stop":
        # Señal octagonal de STOP
        bpy.ops.mesh.primitive_cylinder_add(vertices=8, radius=0.8, depth=0.1,
                                          enter_editmode=False,
                                          location=(location[0], location[1], 2.8))
        señal = bpy.context.active_object
        señal.name = f"Señal_Stop_{location[0]}"
        
        # Material rojo para STOP
        mat_señal = bpy.data.materials.new(name="Material_Stop")
        mat_señal.use_nodes = True
        nodes = mat_señal.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (0.8, 0.1, 0.1, 1.0)  # Rojo
        bsdf.inputs[19].default_value = 0.3  # Emisión
        
    elif tipo == "velocidad":
        # Señal circular de velocidad
        bpy.ops.mesh.primitive_cylinder_add(radius=0.6, depth=0.1,
                                          enter_editmode=False,
                                          location=(location[0], location[1], 2.8))
        señal = bpy.context.active_object
        señal.name = f"Señal_Velocidad_{location[0]}"
        
        # Material blanco con borde rojo
        mat_señal = bpy.data.materials.new(name="Material_Velocidad")
        mat_señal.use_nodes = True
        nodes = mat_señal.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (1.0, 1.0, 1.0, 1.0)  # Blanco
        bsdf.inputs[19].default_value = 0.2  # Emisión
        
    else:  # advertencia
        # Señal triangular de advertencia
        bpy.ops.mesh.primitive_cone_add(vertices=3, radius1=0.7, depth=0.1,
                                      enter_editmode=False,
                                      location=(location[0], location[1], 2.8))
        señal = bpy.context.active_object
        señal.name = f"Señal_Advertencia_{location[0]}"
        
        # Material amarillo para advertencia
        mat_señal = bpy.data.materials.new(name="Material_Advertencia")
        mat_señal.use_nodes = True
        nodes = mat_señal.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (1.0, 0.8, 0.1, 1.0)  # Amarillo
        bsdf.inputs[19].default_value = 0.3  # Emisión
    
    señal.data.materials.append(mat_señal)
    
    return poste, señal

# Función para crear guardarraíles
def crear_guardarrail(inicio, fin):
    # Calcular dirección y longitud
    direccion = (fin[0] - inicio[0], fin[1] - inicio[1])
    longitud = math.sqrt(direccion[0]**2 + direccion[1]**2)
    
    # Crear el rail principal
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, 
                                  location=(inicio[0] + direccion[0]/2, 
                                          inicio[1] + direccion[1]/2, 0.8))
    rail = bpy.context.active_object
    rail.name = f"Guardarrail_{inicio}_{fin}"
    rail.scale = (longitud/2, 0.1, 0.3)
    
    # Rotar para alinear con la dirección
    angulo = math.atan2(direccion[1], direccion[0])
    rail.rotation_euler.z = angulo
    
    # Material metálico para guardarrail
    mat_rail = bpy.data.materials.new(name=f"Material_Rail_{inicio}")
    mat_rail.use_nodes = True
    nodes = mat_rail.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.7, 0.7, 0.7, 1.0)  # Gris claro
    bsdf.inputs[4].default_value = 0.9  # Metálico
    bsdf.inputs[7].default_value = 0.1  # Rugosidad baja
    rail.data.materials.append(mat_rail)
    
    # Crear postes de soporte
    postes = []
    num_postes = int(longitud / 4)  # Un poste cada 4 unidades
    for i in range(num_postes + 1):
        factor = i / num_postes if num_postes > 0 else 0
        pos_x = inicio[0] + direccion[0] * factor
        pos_y = inicio[1] + direccion[1] * factor
        
        bpy.ops.mesh.primitive_cylinder_add(radius=0.05, depth=1.5,
                                          enter_editmode=False,
                                          location=(pos_x, pos_y, 0.75))
        poste = bpy.context.active_object
        poste.name = f"Poste_Rail_{i}_{inicio}"
        poste.data.materials.append(mat_rail)
        postes.append(poste)
    
    return rail, postes

# Función para crear vehículos
def crear_vehiculo(location, tipo="coche"):
    if tipo == "coche":
        # Carrocería principal
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, 
                                      location=(location[0], location[1], 0.8))
        carroceria = bpy.context.active_object
        carroceria.name = f"Coche_{location[0]}_{location[1]}"
        carroceria.scale = (2, 1, 0.6)
        
        # Material para coche
        mat_coche = bpy.data.materials.new(name=f"Material_Coche_{location[0]}")
        mat_coche.use_nodes = True
        nodes = mat_coche.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        color = random.choice([(0.8, 0.1, 0.1, 1.0), (0.1, 0.1, 0.8, 1.0), 
                              (0.1, 0.6, 0.1, 1.0), (0.8, 0.8, 0.8, 1.0)])
        bsdf.inputs[0].default_value = color
        bsdf.inputs[4].default_value = 0.8  # Metálico
        bsdf.inputs[7].default_value = 0.1  # Rugosidad
        carroceria.data.materials.append(mat_coche)
        
        # Crear ruedas
        ruedas = []
        posiciones_ruedas = [(-0.8, -0.6, 0.3), (-0.8, 0.6, 0.3), 
                           (0.8, -0.6, 0.3), (0.8, 0.6, 0.3)]
        
        for i, pos_rueda in enumerate(posiciones_ruedas):
            bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=0.2,
                                              enter_editmode=False,
                                              location=(location[0] + pos_rueda[0],
                                                      location[1] + pos_rueda[1],
                                                      pos_rueda[2]))
            rueda = bpy.context.active_object
            rueda.name = f"Rueda_{i}_{location[0]}"
            rueda.rotation_euler.y = math.radians(90)
            
            # Material para ruedas
            mat_rueda = bpy.data.materials.new(name=f"Material_Rueda_{i}")
            mat_rueda.use_nodes = True
            nodes = mat_rueda.node_tree.nodes
            bsdf = nodes.get("Principled BSDF")
            bsdf.inputs[0].default_value = (0.1, 0.1, 0.1, 1.0)  # Negro
            bsdf.inputs[7].default_value = 0.9  # Rugosidad alta
            rueda.data.materials.append(mat_rueda)
            ruedas.append(rueda)
        
        return carroceria, ruedas
    
    else:  # camión
        # Cabina del camión
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, 
                                      location=(location[0], location[1], 1.2))
        cabina = bpy.context.active_object
        cabina.name = f"Camion_Cabina_{location[0]}"
        cabina.scale = (1.5, 1, 1)
        
        # Remolque
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, 
                                      location=(location[0] + 3, location[1], 1))
        remolque = bpy.context.active_object
        remolque.name = f"Camion_Remolque_{location[0]}"
        remolque.scale = (3, 1, 0.8)
        
        # Material para camión
        mat_camion = bpy.data.materials.new(name=f"Material_Camion_{location[0]}")
        mat_camion.use_nodes = True
        nodes = mat_camion.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (0.8, 0.4, 0.1, 1.0)  # Naranja
        bsdf.inputs[4].default_value = 0.6  # Metálico
        bsdf.inputs[7].default_value = 0.3  # Rugosidad
        
        cabina.data.materials.append(mat_camion)
        remolque.data.materials.append(mat_camion)
        
        return cabina, remolque

# Crear elementos de la carretera
carretera = crear_carretera_principal()
lineas = crear_lineas_carretera()

# Crear señales de tráfico a lo largo de la carretera
señales = []
posiciones_señales = [
    (15, -8, "stop"), (45, 12, "velocidad"), (75, -12, "advertencia"),
    (105, 8, "stop"), (135, -6, "velocidad")
]

for pos_x, pos_y, tipo in posiciones_señales:
    señal = crear_señal_trafico((pos_x, pos_y, 0), tipo)
    señales.append(señal)

# Crear guardarraíles en curvas peligrosas
guardarrailes = []
tramos_rail = [
    ((35, -15), (45, -5)), ((75, 10), (85, 20)), ((115, -10), (125, 0))
]

for inicio, fin in tramos_rail:
    rail = crear_guardarrail(inicio, fin)
    guardarrailes.append(rail)

# Crear vehículos en la carretera
vehiculos = []
posiciones_vehiculos = [
    (25, 1, "coche"), (55, -2, "camion"), (85, 1.5, "coche"),
    (115, -1, "coche"), (130, 0.5, "camion")
]

for pos_x, pos_y, tipo in posiciones_vehiculos:
    vehiculo = crear_vehiculo((pos_x, pos_y, 0), tipo)
    vehiculos.append(vehiculo)

# Crear paisaje alrededor de la carretera
def crear_arbol_carretera(location):
    altura = random.uniform(3, 6)
    bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=altura,
                                      enter_editmode=False,
                                      location=(location[0], location[1], altura/2))
    tronco = bpy.context.active_object
    tronco.name = f"Arbol_Tronco_{location[0]}_{location[1]}"

    # Copa del árbol
    bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(1.5, 2.5),
                                        enter_editmode=False,
                                        location=(location[0], location[1], altura + 1))
    copa = bpy.context.active_object
    copa.name = f"Arbol_Copa_{location[0]}_{location[1]}"

    # Materiales
    mat_tronco = bpy.data.materials.new(name=f"Mat_Tronco_{location[0]}")
    mat_tronco.use_nodes = True
    nodes = mat_tronco.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.2, 0.1, 1.0)
    tronco.data.materials.append(mat_tronco)

    mat_copa = bpy.data.materials.new(name=f"Mat_Copa_{location[0]}")
    mat_copa.use_nodes = True
    nodes = mat_copa.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.2, 0.5, 0.1, 1.0)
    copa.data.materials.append(mat_copa)

    return tronco, copa

# Crear árboles a los lados de la carretera
arboles = []
for i in range(50):
    # Lado izquierdo
    x = random.uniform(0, 140)
    y = random.uniform(-20, -8)
    arbol = crear_arbol_carretera((x, y, 0))
    arboles.append(arbol)

    # Lado derecho
    x = random.uniform(0, 140)
    y = random.uniform(8, 20)
    arbol = crear_arbol_carretera((x, y, 0))
    arboles.append(arbol)

# Crear terreno base
bpy.ops.mesh.primitive_plane_add(size=300, enter_editmode=False, location=(70, 0, -0.1))
terreno = bpy.context.active_object
terreno.name = "Terreno_Base"

# Material para terreno
mat_terreno = bpy.data.materials.new(name="Material_Terreno")
mat_terreno.use_nodes = True
nodes = mat_terreno.node_tree.nodes
bsdf = nodes.get("Principled BSDF")
bsdf.inputs[0].default_value = (0.3, 0.5, 0.2, 1.0)  # Verde césped
bsdf.inputs[7].default_value = 0.8
terreno.data.materials.append(mat_terreno)

# Añadir iluminación
# Sol principal
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 50))
sol = bpy.context.active_object
sol.name = "Sol"
sol.data.energy = 3
sol.rotation_euler = (math.radians(45), 0, math.radians(30))

# Luces de carretera (farolas)
farolas = []
for i in range(0, 140, 20):
    # Farola izquierda
    bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=6,
                                      enter_editmode=False,
                                      location=(i, -6, 3))
    poste_farola = bpy.context.active_object
    poste_farola.name = f"Farola_Poste_{i}"

    # Luz de la farola
    bpy.ops.object.light_add(type='POINT', radius=1, location=(i, -6, 5.5))
    luz_farola = bpy.context.active_object
    luz_farola.name = f"Farola_Luz_{i}"
    luz_farola.data.energy = 100
    luz_farola.data.color = (1.0, 0.9, 0.7)  # Luz cálida

    # Material para poste
    mat_farola = bpy.data.materials.new(name=f"Material_Farola_{i}")
    mat_farola.use_nodes = True
    nodes = mat_farola.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.2, 0.2, 0.2, 1.0)
    bsdf.inputs[4].default_value = 0.8
    poste_farola.data.materials.append(mat_farola)

    farolas.append((poste_farola, luz_farola))

# Añadir cámara con vista de la carretera
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW',
                        location=(70, -30, 15),
                        rotation=(math.radians(70), 0, 0))
camara = bpy.context.active_object
camara.name = "Camara_Carretera"
bpy.context.scene.camera = camara

# Configurar fondo de cielo
world = bpy.context.scene.world
world.use_nodes = True
bg = world.node_tree.nodes['Background']
bg.inputs[0].default_value = (0.5, 0.7, 1.0, 1.0)  # Azul cielo
bg.inputs[1].default_value = 1.0

print("¡Carretera transitable creada con éxito!")
print("Incluye: carretera serpenteante, señales de tráfico, guardarraíles, vehículos y paisaje")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
