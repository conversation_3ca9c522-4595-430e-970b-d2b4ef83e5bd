import bpy
import math
import random

# Limpiar escena
if bpy.context.mode != 'OBJECT':
    bpy.ops.object.mode_set(mode='OBJECT')

# Seleccionar todos los objetos
for obj in bpy.context.scene.objects:
    obj.select_set(True)

# Eliminar todos los objetos
bpy.ops.object.delete(use_global=False)

# Función para crear el Sol
def crear_sol():
    bpy.ops.mesh.primitive_ico_sphere_add(radius=5, enter_editmode=False, location=(0, 0, 0))
    sol = bpy.context.active_object
    sol.name = "Sol"
    
    # Material para el Sol con emisión
    mat_sol = bpy.data.materials.new(name="Material_Sol")
    mat_sol.use_nodes = True
    nodes = mat_sol.node_tree.nodes
    links = mat_sol.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para el Sol
    output = nodes.new(type='ShaderNodeOutputMaterial')
    emission = nodes.new(type='ShaderNodeEmission')
    noise = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    
    # Configurar emisión del Sol
    emission.inputs['Color'].default_value = (1.0, 0.8, 0.3, 1.0)  # Amarillo-naranja
    emission.inputs['Strength'].default_value = 10.0
    
    # Configurar ruido para superficie solar
    noise.inputs['Scale'].default_value = 5.0
    noise.inputs['Detail'].default_value = 10.0
    
    # ColorRamp para variaciones de color
    color_ramp.color_ramp.elements[0].color = (1.0, 0.6, 0.1, 1.0)  # Naranja
    color_ramp.color_ramp.elements[1].color = (1.0, 1.0, 0.8, 1.0)  # Amarillo claro
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], emission.inputs['Color'])
    links.new(emission.outputs['Emission'], output.inputs['Surface'])
    
    # Aplicar material
    sol.data.materials.append(mat_sol)
    
    # Añadir luz del Sol
    bpy.ops.object.light_add(type='POINT', radius=1, location=(0, 0, 0))
    luz_sol = bpy.context.active_object
    luz_sol.name = "Luz_Sol"
    luz_sol.data.energy = 1000
    luz_sol.data.color = (1.0, 0.9, 0.7)
    
    return sol, luz_sol

# Función para crear planetas
def crear_planeta(nombre, radio, distancia, color, tiene_anillos=False, lunas=0):
    # Crear el planeta
    bpy.ops.mesh.primitive_ico_sphere_add(radius=radio, enter_editmode=False, 
                                        location=(distancia, 0, 0))
    planeta = bpy.context.active_object
    planeta.name = nombre
    
    # Material para el planeta
    mat_planeta = bpy.data.materials.new(name=f"Material_{nombre}")
    mat_planeta.use_nodes = True
    nodes = mat_planeta.node_tree.nodes
    links = mat_planeta.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para el planeta
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    
    # Configurar propiedades del planeta
    principled.inputs['Base Color'].default_value = color
    principled.inputs['Roughness'].default_value = 0.8
    principled.inputs['Specular'].default_value = 0.3
    
    # Configurar ruido para superficie
    noise.inputs['Scale'].default_value = 10.0
    noise.inputs['Detail'].default_value = 8.0
    
    # ColorRamp para variaciones
    color_ramp.color_ramp.elements[0].color = (color[0] * 0.7, color[1] * 0.7, color[2] * 0.7, 1.0)
    color_ramp.color_ramp.elements[1].color = (color[0] * 1.3, color[1] * 1.3, color[2] * 1.3, 1.0)
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Aplicar material
    planeta.data.materials.append(mat_planeta)
    
    # Crear anillos si es necesario (Saturno)
    anillos = None
    if tiene_anillos:
        bpy.ops.mesh.primitive_torus_add(major_radius=radio * 2, minor_radius=radio * 0.1,
                                       enter_editmode=False, location=(distancia, 0, 0))
        anillos = bpy.context.active_object
        anillos.name = f"Anillos_{nombre}"
        anillos.rotation_euler.x = math.radians(15)  # Inclinar los anillos
        
        # Material para anillos
        mat_anillos = bpy.data.materials.new(name=f"Material_Anillos_{nombre}")
        mat_anillos.use_nodes = True
        nodes = mat_anillos.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (0.8, 0.7, 0.5, 1.0)  # Beige
        bsdf.inputs[21].default_value = 0.7  # Alpha para transparencia
        mat_anillos.blend_method = 'BLEND'
        anillos.data.materials.append(mat_anillos)
    
    # Crear lunas
    lunas_objetos = []
    for i in range(lunas):
        radio_luna = radio * random.uniform(0.1, 0.3)
        distancia_luna = radio + random.uniform(radio * 1.5, radio * 3)
        angulo_luna = (2 * math.pi * i) / lunas
        
        pos_x = distancia + distancia_luna * math.cos(angulo_luna)
        pos_y = distancia_luna * math.sin(angulo_luna)
        
        bpy.ops.mesh.primitive_ico_sphere_add(radius=radio_luna, enter_editmode=False,
                                            location=(pos_x, pos_y, 0))
        luna = bpy.context.active_object
        luna.name = f"Luna_{i}_{nombre}"
        
        # Material para luna
        mat_luna = bpy.data.materials.new(name=f"Material_Luna_{i}_{nombre}")
        mat_luna.use_nodes = True
        nodes = mat_luna.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (0.6, 0.6, 0.6, 1.0)  # Gris
        bsdf.inputs[7].default_value = 0.9  # Rugosidad alta
        luna.data.materials.append(mat_luna)
        
        lunas_objetos.append(luna)
    
    return planeta, anillos, lunas_objetos

# Función para crear cinturón de asteroides
def crear_cinturon_asteroides():
    asteroides = []
    
    for i in range(200):
        # Posición aleatoria en el cinturón
        distancia = random.uniform(35, 45)
        angulo = random.uniform(0, 2 * math.pi)
        altura = random.uniform(-2, 2)
        
        pos_x = distancia * math.cos(angulo)
        pos_y = distancia * math.sin(angulo)
        pos_z = altura
        
        # Tamaño aleatorio
        tamaño = random.uniform(0.1, 0.5)
        
        # Crear asteroide irregular
        bpy.ops.mesh.primitive_ico_sphere_add(radius=tamaño, enter_editmode=False,
                                            location=(pos_x, pos_y, pos_z))
        asteroide = bpy.context.active_object
        asteroide.name = f"Asteroide_{i}"
        
        # Deformar para hacer irregular
        bpy.context.view_layer.objects.active = asteroide
        bpy.ops.object.mode_set(mode='EDIT')
        bpy.ops.transform.resize(value=(random.uniform(0.5, 1.5), 
                                      random.uniform(0.5, 1.5), 
                                      random.uniform(0.5, 1.5)))
        bpy.ops.object.mode_set(mode='OBJECT')
        
        # Material para asteroide
        mat_asteroide = bpy.data.materials.new(name=f"Material_Asteroide_{i}")
        mat_asteroide.use_nodes = True
        nodes = mat_asteroide.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        gris = random.uniform(0.2, 0.5)
        bsdf.inputs[0].default_value = (gris, gris * 0.8, gris * 0.6, 1.0)
        bsdf.inputs[7].default_value = 0.9
        asteroide.data.materials.append(mat_asteroide)
        
        asteroides.append(asteroide)
    
    return asteroides

# Función para crear órbitas visibles
def crear_orbita(radio):
    # Deseleccionar todo primero
    for obj in bpy.context.scene.objects:
        obj.select_set(False)

    bpy.ops.curve.primitive_bezier_circle_add(radius=radio, enter_editmode=False, location=(0, 0, 0))
    orbita = bpy.context.active_object
    orbita.name = f"Orbita_{radio}"

    # Configurar la curva
    orbita.data.bevel_depth = 0.02
    orbita.data.resolution_u = 64

    # Asegurar que el objeto esté seleccionado antes de convertir
    bpy.context.view_layer.objects.active = orbita
    orbita.select_set(True)

    # Convertir a mesh
    bpy.ops.object.convert(target='MESH')

    # Material para órbita
    mat_orbita = bpy.data.materials.new(name=f"Material_Orbita_{radio}")
    mat_orbita.use_nodes = True
    nodes = mat_orbita.node_tree.nodes
    links = mat_orbita.node_tree.links

    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)

    # Crear nodos para órbita emisiva
    output = nodes.new(type='ShaderNodeOutputMaterial')
    emission = nodes.new(type='ShaderNodeEmission')

    # Configurar emisión para visibilidad
    emission.inputs['Color'].default_value = (0.5, 0.5, 1.0, 1.0)  # Azul claro
    emission.inputs['Strength'].default_value = 0.5

    # Conectar nodos
    links.new(emission.outputs['Emission'], output.inputs['Surface'])

    # Aplicar material
    orbita.data.materials.append(mat_orbita)

    return orbita

# Crear el Sol
sol, luz_sol = crear_sol()

# Datos de los planetas (nombre, radio, distancia, color, anillos, lunas)
planetas_data = [
    ("Mercurio", 0.4, 12, (0.7, 0.7, 0.7, 1.0), False, 0),
    ("Venus", 0.9, 18, (1.0, 0.8, 0.3, 1.0), False, 0),
    ("Tierra", 1.0, 25, (0.2, 0.5, 1.0, 1.0), False, 1),
    ("Marte", 0.5, 32, (0.8, 0.3, 0.1, 1.0), False, 2),
    ("Jupiter", 3.0, 55, (0.8, 0.6, 0.3, 1.0), False, 4),
    ("Saturno", 2.5, 70, (0.9, 0.8, 0.5, 1.0), True, 3),
    ("Urano", 1.8, 85, (0.3, 0.8, 0.9, 1.0), False, 2),
    ("Neptuno", 1.7, 100, (0.2, 0.3, 0.9, 1.0), False, 1)
]

# Crear planetas y órbitas
planetas = []
orbitas = []

for nombre, radio, distancia, color, anillos, lunas in planetas_data:
    planeta, anillo, lunas_obj = crear_planeta(nombre, radio, distancia, color, anillos, lunas)
    planetas.append((planeta, anillo, lunas_obj))
    
    # Crear órbita visible
    orbita = crear_orbita(distancia)
    orbitas.append(orbita)

# Crear cinturón de asteroides
asteroides = crear_cinturon_asteroides()

# Crear cometa
def crear_cometa():
    # Núcleo del cometa
    bpy.ops.mesh.primitive_ico_sphere_add(radius=0.3, enter_editmode=False, location=(120, 20, 5))
    nucleo = bpy.context.active_object
    nucleo.name = "Cometa_Nucleo"
    
    # Cola del cometa
    bpy.ops.mesh.primitive_cone_add(radius1=2, radius2=0.1, depth=15,
                                  enter_editmode=False, location=(115, 18, 5))
    cola = bpy.context.active_object
    cola.name = "Cometa_Cola"
    cola.rotation_euler.y = math.radians(45)
    
    # Materiales
    mat_nucleo = bpy.data.materials.new(name="Material_Cometa_Nucleo")
    mat_nucleo.use_nodes = True
    nodes = mat_nucleo.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.8, 0.8, 0.9, 1.0)
    bsdf.inputs[19].default_value = 0.3
    nucleo.data.materials.append(mat_nucleo)
    
    mat_cola = bpy.data.materials.new(name="Material_Cometa_Cola")
    mat_cola.use_nodes = True
    nodes = mat_cola.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.7, 0.9, 1.0, 1.0)
    bsdf.inputs[19].default_value = 1.0
    bsdf.inputs[21].default_value = 0.2
    mat_cola.blend_method = 'BLEND'
    cola.data.materials.append(mat_cola)
    
    return nucleo, cola

cometa = crear_cometa()

# Añadir cámara con vista del sistema solar
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW', 
                        location=(150, -150, 80), 
                        rotation=(math.radians(60), 0, math.radians(45)))
camara = bpy.context.active_object
camara.name = "Camara_Sistema_Solar"
bpy.context.scene.camera = camara

# Configurar fondo del espacio con estrellas
world = bpy.context.scene.world
world.use_nodes = True
nodes = world.node_tree.nodes
links = world.node_tree.links

# Limpiar nodos existentes
for node in nodes:
    nodes.remove(node)

# Crear nodos para el espacio
background = nodes.new(type='ShaderNodeBackground')
output = nodes.new(type='ShaderNodeOutputWorld')
tex_coord = nodes.new(type='ShaderNodeTexCoord')
mapping = nodes.new(type='ShaderNodeMapping')
noise_texture = nodes.new(type='ShaderNodeTexNoise')
color_ramp = nodes.new(type='ShaderNodeValToRGB')
mix_rgb = nodes.new(type='ShaderNodeMixRGB')

# Configurar fondo del espacio
background.inputs['Color'].default_value = (0.01, 0.01, 0.05, 1.0)  # Azul muy oscuro
background.inputs['Strength'].default_value = 1.0

# Configurar ruido para estrellas
noise_texture.inputs['Scale'].default_value = 1000.0  # Muchas estrellas
noise_texture.inputs['Detail'].default_value = 15.0
noise_texture.inputs['Roughness'].default_value = 0.5

# ColorRamp para estrellas
color_ramp.color_ramp.elements[0].position = 0.98  # Solo los puntos más brillantes
color_ramp.color_ramp.elements[0].color = (0.0, 0.0, 0.0, 1.0)  # Negro
color_ramp.color_ramp.elements[1].position = 1.0
color_ramp.color_ramp.elements[1].color = (1.0, 1.0, 1.0, 1.0)  # Blanco brillante

# Añadir punto intermedio para estrellas de diferentes colores
color_ramp.color_ramp.elements.new(0.99)
color_ramp.color_ramp.elements[1].color = (0.8, 0.9, 1.0, 1.0)  # Azul claro

# Configurar mezcla
mix_rgb.blend_type = 'ADD'
mix_rgb.inputs['Fac'].default_value = 1.0
mix_rgb.inputs[1].default_value = (0.01, 0.01, 0.05, 1.0)  # Color base del espacio

# Conectar nodos
links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
links.new(color_ramp.outputs['Color'], mix_rgb.inputs[2])
links.new(mix_rgb.outputs['Color'], background.inputs['Color'])
links.new(background.outputs['Background'], output.inputs['Surface'])

# Función para animar rotación de planetas (opcional)
def configurar_animacion():
    # Configurar frame range
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 250

    # Animar rotación del Sol
    sol.rotation_euler = (0, 0, 0)
    sol.keyframe_insert(data_path="rotation_euler", frame=1)
    sol.rotation_euler = (0, 0, math.radians(360))
    sol.keyframe_insert(data_path="rotation_euler", frame=250)

    # Animar órbitas de planetas
    for i, planeta_data in enumerate(planetas_data):
        nombre = planeta_data[0]
        distancia = planeta_data[2]
        planeta_obj = planetas[i][0]

        # Velocidad orbital (más lento para planetas más lejanos)
        velocidad = 1.0 / (distancia * 0.1)

        # Crear empty para órbita
        bpy.ops.object.empty_add(type='PLAIN_AXES', location=(0, 0, 0))
        empty_orbita = bpy.context.active_object
        empty_orbita.name = f"Orbita_Empty_{nombre}"

        # Hacer que el planeta sea hijo del empty
        planeta_obj.parent = empty_orbita
        planeta_obj.parent_type = 'OBJECT'

        # Animar rotación del empty
        empty_orbita.rotation_euler = (0, 0, 0)
        empty_orbita.keyframe_insert(data_path="rotation_euler", frame=1)
        empty_orbita.rotation_euler = (0, 0, math.radians(360 * velocidad))
        empty_orbita.keyframe_insert(data_path="rotation_euler", frame=250)

        # Configurar interpolación lineal
        if empty_orbita.animation_data:
            for fcurve in empty_orbita.animation_data.action.fcurves:
                for keyframe in fcurve.keyframe_points:
                    keyframe.interpolation = 'LINEAR'

# Configurar animación
configurar_animacion()

# Añadir iluminación adicional para mejor visibilidad
bpy.ops.object.light_add(type='SUN', radius=1, location=(200, 200, 100))
luz_ambiente = bpy.context.active_object
luz_ambiente.name = "Luz_Ambiente"
luz_ambiente.data.energy = 0.5
luz_ambiente.data.color = (0.8, 0.9, 1.0)  # Luz azulada suave

print("¡Sistema Solar 3D creado con éxito!")
print("Incluye: Sol, 8 planetas con lunas, cinturón de asteroides, cometa y animación orbital")
print("Para ver la animación, presiona Espacio o usa el timeline")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
